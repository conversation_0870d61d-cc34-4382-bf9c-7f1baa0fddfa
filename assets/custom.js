console.log("test");

document.addEventListener("DOMContentLoaded", function () {
    const header = document.querySelector('.header__columns.spacing-style');

    if (!header) return;

    // Initial margin (you can adjust if needed)
    header.style.transition = "margin-top 0.3s ease";

    window.addEventListener('scroll', function () {
      if (window.scrollY > 50) {
        header.style.marginTop = "0px";
      } else {
        header.style.marginTop = ""; // Or set to original value like "20px"
      }
    });
  });


  document.addEventListener("DOMContentLoaded", function () {
    const bars = document.querySelectorAll('[class^="ai-bar-fill-"]');

    const observer = new IntersectionObserver((entries, obs) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const bar = entry.target;
          const finalWidth = bar.getAttribute("data-width");
            console.log(bar);
          if (finalWidth) {
            bar.style.width = finalWidth;
          }
          
          obs.unobserve(bar); // prevent it from triggering again
        }
      });
    }, {
      threshold: 0.5 // element must be at least 50% in view
    });

    bars.forEach(bar => {
      observer.observe(bar);
    });
  });

  // Auto-add IDs to headings for internal page anchor links
  document.addEventListener("DOMContentLoaded", function () {
    const internalPageContent = document.querySelector('.internal-page-content');

    if (internalPageContent) {
      // First try to find standard headings
      let headings = internalPageContent.querySelectorAll('h1, h2, h3, h4, h5, h6');

      // If no standard headings found, look for other potential title elements
      if (headings.length === 0) {
        headings = internalPageContent.querySelectorAll('p strong, p b, strong, b, .title, .heading');
      }

      // If still no headings, try to find paragraphs that might be titles (first few paragraphs)
      if (headings.length === 0) {
        const allParagraphs = internalPageContent.querySelectorAll('p');
        headings = Array.from(allParagraphs).slice(0, 8); // Take first 8 paragraphs as potential titles
      }

      console.log('Found headings:', headings.length); // Debug log
      console.log('Content HTML:', internalPageContent.innerHTML.substring(0, 500)); // Debug log

      headings.forEach((heading, index) => {
        // Add ID based on index (1-based to match the anchor links)
        const id = (index + 1).toString();
        heading.id = id;
        console.log(`Added ID "${id}" to:`, heading.textContent?.substring(0, 50)); // Debug log
      });

      // Handle smooth scrolling for anchor links
      const anchorLinks = document.querySelectorAll('.internal-page-sidebar a[href^="#"]');

      anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();

          const targetId = this.getAttribute('href').substring(1);
          const targetElement = document.getElementById(targetId);

          console.log('Clicking link to:', targetId, 'Found element:', targetElement); // Debug log

          if (targetElement) {
            targetElement.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });

            // Add a small offset to account for any fixed headers
            setTimeout(() => {
              window.scrollBy(0, -20);
            }, 100);
          } else {
            console.log('Target element not found for ID:', targetId);
          }
        });
      });
    } else {
      console.log('Internal page content not found');
    }
  });

  // CUSTOM Animated Stats Counter for SP Our Stats Count section in Our-Scientists page
  document.addEventListener("DOMContentLoaded", function () {
    const statNumbers = document.querySelectorAll('.stat-number');

    function parseNumberAndFormat(originalText) {
      // Extract numeric value and preserve formatting
      const numericMatch = originalText.match(/\d+/);
      if (!numericMatch) return null;
      
      let targetValue = parseInt(numericMatch[0]);
      
      // Handle K, M multipliers
      if (originalText.includes('K')) targetValue *= 1000;
      if (originalText.includes('M')) targetValue *= 1000000;
      
      return {
        target: targetValue,
        prefix: originalText.substring(0, numericMatch.index),
        suffix: originalText.substring(numericMatch.index + numericMatch[0].length)
      };
    }

    function animateNumber(element, numberData, duration = 2000) {
      const start = 0;
      const startTime = performance.now();
      
      function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // Easing function for smooth animation (ease-out)
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = Math.floor(start + (numberData.target - start) * easeOutQuart);
        
        element.textContent = numberData.prefix + current.toLocaleString() + numberData.suffix;
        
        if (progress < 1) {
          requestAnimationFrame(updateNumber);
        } else {
          // Ensure we end with the exact original text
          element.textContent = element.dataset.original;
        }
      }
      
      requestAnimationFrame(updateNumber);
    }

    const statsObserver = new IntersectionObserver((entries, obs) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const statElement = entry.target;
          const originalText = statElement.dataset.original;
          const numberData = parseNumberAndFormat(originalText);
          
          if (numberData) {
            animateNumber(statElement, numberData);
          }
          
          obs.unobserve(statElement); // prevent it from triggering again
        }
      });
    }, {
      threshold: 0.5 // element must be at least 50% in view
    });

    statNumbers.forEach(stat => {
      statsObserver.observe(stat);
    });
  });

